﻿using System.Linq;
using System.Threading.Tasks;
using Microsoft.Owin;

namespace ActiveInfoSSO.Middlewares;

public class SignoutMiddleware(OwinMiddleware next) : OwinMiddleware(next)
{
    public override async Task Invoke(IOwinContext context)
    {
        if (!context.Request.Uri.Segments.Contains("logout"))
        {
            await Next.Invoke(context);
            return;
        }

        if (context.Authentication.User.Identity != null && context.Authentication.User.Identity.IsAuthenticated)
        {
            var token = context.Authentication.User.Claims.FirstOrDefault(x => x.Type == "id_token");
            if (token != null)
                context.Request.QueryString = context.Request.QueryString.Value != string.Empty
                    ? new QueryString($"{context.Request.QueryString.Value}&id_token_hint={token}")
                    : new QueryString($"id_token_hint={token}");
        }

        await Next.Invoke(context);
    }
}