﻿using Microsoft.Owin;
using Microsoft.Owin.Infrastructure;

namespace ActiveInfoSSO.Utils;

public class SameSiteCookieManager(ICookieManager innerManager) : ICookieManager
{
    public SameSiteCookieManager() : this(new CookieManager())
    {
    }

    public void AppendResponseCookie(IOwinContext context, string key, string value,
        CookieOptions options)
    {
        options = CheckSameSite(context, options);
        innerManager.AppendResponseCookie(context, key, value, options);
    }

    public void DeleteCookie(IOwinContext context, string key, CookieOptions options)
    {
        options = CheckSameSite(context, options);
        innerManager.DeleteCookie(context, key, options);
    }

    public string GetRequestCookie(IOwinContext context, string key)
    {
        return innerManager.GetRequestCookie(context, key);
    }

    private static CookieOptions CheckSameSite(IOwinContext context, CookieOptions options)
    {
        if (options.SameSite == SameSiteMode.None && DisallowsSameSiteNone(context))
            options.SameSite = null;

        return options;
    }

    private static bool DisallowsSameSiteNone(IOwinContext context)
    {
        var userAgent = context.Request.Headers["User-Agent"];

        if (string.IsNullOrEmpty(userAgent))
            return false;

        if (userAgent.Contains("CPU iPhone OS 12") || userAgent.Contains("iPad; CPU OS 12"))
            return true;

        if (userAgent.Contains("Macintosh; Intel Mac OS X 10_14") && userAgent.Contains("Version/") && userAgent.Contains("Safari"))
            return true;

        return userAgent.Contains("Chrome/5") || userAgent.Contains("Chrome/6");
    }
}