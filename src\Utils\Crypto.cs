﻿using System;
using System.Configuration;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace ActiveInfoSSO.Utils;

/// <summary>
/// Summary description for Crypto
/// </summary>
public static class Crypto
{
    private const int Iterations = 100000;
    private const int KeySize = 32;
    private const int IvSize = 16;

    public static string Encrypt(string clearText)
    {
        var encryptionKey = ConfigurationManager.AppSettings["AccessKey"];
        var clearBytes = Encoding.Unicode.GetBytes(clearText);
        var salt = GenerateSalt();

        using var encryptor = Aes.Create();
        var pdb = new Rfc2898DeriveBytes(encryptionKey, salt, Iterations, HashAlgorithmName.SHA256);
        encryptor.Key = pdb.GetBytes(KeySize);
        encryptor.IV = pdb.GetBytes(IvSize);

        using var ms = new MemoryStream();
        using (var cs = new CryptoStream(ms, encryptor.CreateEncryptor(), CryptoStreamMode.Write))
        {
            cs.Write(clearBytes, 0, clearBytes.Length);
            cs.Close();
        }

        var encryptedData = ms.ToArray();
        var result = new byte[salt.Length + encryptedData.Length];
        Buffer.BlockCopy(salt, 0, result, 0, salt.Length);
        Buffer.BlockCopy(encryptedData, 0, result, salt.Length, encryptedData.Length);

        return Convert.ToBase64String(result);
    }

    public static string Decrypt(string cipherText)
    {
        var encryptionKey = ConfigurationManager.AppSettings["AccessKey"];
        var cipherBytes = Convert.FromBase64String(cipherText);
        var salt = new byte[KeySize];
        Buffer.BlockCopy(cipherBytes, 0, salt, 0, salt.Length);
        var encryptedData = new byte[cipherBytes.Length - salt.Length];
        Buffer.BlockCopy(cipherBytes, salt.Length, encryptedData, 0, encryptedData.Length);

        using var encryptor = Aes.Create();
        var pdb = new Rfc2898DeriveBytes(encryptionKey, salt, Iterations, HashAlgorithmName.SHA256);
        encryptor.Key = pdb.GetBytes(KeySize);
        encryptor.IV = pdb.GetBytes(IvSize);

        using var ms = new MemoryStream();
        using (var cs = new CryptoStream(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Write))
        {
            cs.Write(encryptedData, 0, encryptedData.Length);
            cs.Close();
        }

        return Encoding.Unicode.GetString(ms.ToArray());
    }

    private static byte[] GenerateSalt()
    {
        using var rng = new RNGCryptoServiceProvider();
        var salt = new byte[KeySize];
        rng.GetBytes(salt);
        return salt;
    }
}
