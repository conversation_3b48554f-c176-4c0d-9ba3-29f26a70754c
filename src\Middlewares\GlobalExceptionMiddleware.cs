﻿using log4net;

using Newtonsoft.Json;

using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Owin;

namespace ActiveInfoSSO.Middlewares;

public class GlobalExceptionMiddleware(OwinMiddleware next) : OwinMiddleware(next)
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(GlobalExceptionMiddleware));

    public override async Task Invoke(IOwinContext context)
    {
        try
        {
            await Next.Invoke(context);
        }
        catch (Exception ex)
        {
            try
            {
                HandleException(ex, context);

                Log.Error(ex.Message, ex);
            }
            catch (Exception exi)
            {
                Log.Error(exi.Message, exi);
            }
        }
    }

    private static void HandleException(Exception ex, IOwinContext context)
    {
        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
        context.Response.ReasonPhrase = "Internal Server Error";
        context.Response.ContentType = "application/json";
        context.Response.Write(JsonConvert.SerializeObject(ex));
    }
}