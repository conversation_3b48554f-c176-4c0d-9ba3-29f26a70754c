﻿ using System;
using System.Globalization;
using System.Reflection;
using System.Web;
using System.Web.Security;
using ActiveInfoSSO.Entities;
using Newtonsoft.Json;
using SAML2;
using SAML2.State;

namespace ActiveInfoSSO.Utils;

public static class ExtendAuthCookieAction
{
    public static readonly IInternalStateService StateService = StateServiceProvider.StateServiceFor(MethodBase.GetCurrentMethod()?.DeclaringType ?? typeof(ExtendAuthCookieAction));

    public static void SignOnAction(Assertion assertion)
    {
        try
        {
            var iDpSession = new IdpSession
            {
                Idp = assertion?.Issuer?.Text,
                SessionId = assertion?.ID,
                NameIdFormat = assertion?.Subject?.NameID?.Format,
            };

            var userName = assertion?.AttributeStatement?.Attribute?.AttributeValue?.Text ?? assertion?.Subject?.NameID?.Text;

            var authCookie = HttpContext.Current.Response.Cookies["UserInfo"] ?? new HttpCookie("UserInfo");

            var formsAuthTicket = authCookie.Value != null ? FormsAuthentication.Decrypt(authCookie.Value) : null;

            var issueDate = DateTime.ParseExact(assertion?.IssueInstant, "yyyy-MM-dd'T'HH:mm:ss.fff'Z'", CultureInfo.InvariantCulture);

            var newFormsAuthTicket = new FormsAuthenticationTicket(
                formsAuthTicket?.Version ?? 2,
                formsAuthTicket?.Name ?? userName,
                issueDate,
                issueDate.AddHours(12),
                true,
                JsonConvert.SerializeObject(iDpSession),
                "/");

            var encryptedNewFormsAuthTicket = FormsAuthentication.Encrypt(newFormsAuthTicket);

            authCookie = new HttpCookie("UserInfo")
            {
                Expires = issueDate.AddHours(12),
                Value = encryptedNewFormsAuthTicket
            };

            HttpContext.Current.Response.Cookies.Set(authCookie);
        }
        catch
        {
            // ignored
        }
    }

    public static void SignOnAction(Saml20Assertion assertion, string username = null)
    {
        try
        {
            var iDpSession = new IdpSession
            {
                Idp = assertion?.Issuer,
                SessionId = assertion?.Id,
                NameIdFormat = assertion?.Subject?.Format,
            };

            var userName = username ?? assertion?.Subject?.Value;

            var authCookie = HttpContext.Current.Response.Cookies["UserInfo"] ?? new HttpCookie("UserInfo");

            var formsAuthTicket = authCookie.Value != null ? FormsAuthentication.Decrypt(authCookie.Value) : null;

            var issueDate = assertion?.Assertion.IssueInstant ?? DateTime.ParseExact(assertion?.Assertion.IssueInstantString, "yyyy-MM-dd'T'HH:mm:ss.fff'Z'", CultureInfo.InvariantCulture);

            var newFormsAuthTicket = new FormsAuthenticationTicket(
                formsAuthTicket?.Version ?? 2,
                formsAuthTicket?.Name ?? userName,
                issueDate,
                issueDate.AddHours(12),
                true,
                JsonConvert.SerializeObject(iDpSession),
                "/");

            var encryptedNewFormsAuthTicket = FormsAuthentication.Encrypt(newFormsAuthTicket);

            authCookie = new HttpCookie("UserInfo")
            {
                Expires = issueDate.AddHours(12),
                Value = encryptedNewFormsAuthTicket
            };

            HttpContext.Current.Response.Cookies.Set(authCookie);
        }
        catch
        {
            // ignored
        }
    }

    public static void LogoutAction()
    {
        try
        {
            var formsAuthCookie = HttpContext.Current.Request.Cookies["UserInfo"];

            if (formsAuthCookie?.Value is null or "" or " ")
                return;

            var formsAuthTicket = FormsAuthentication.Decrypt(formsAuthCookie.Value);
            
            if (formsAuthTicket?.UserData is null or "" or " ")
                return;

            var iDpSession = JsonConvert.DeserializeObject<IdpSession>(formsAuthTicket.UserData);

            StateService.Set("LoginIDPId", StateService.Get<string>("TempIDPId"));
            StateService.Set("IDPSessionID", iDpSession.SessionId);
            StateService.Set("IDPNameIdFormat", iDpSession.NameIdFormat);
            StateService.Set("IDPNameId", formsAuthTicket.Name);
        }
        catch
        {
            // ignored
        }
    }
}