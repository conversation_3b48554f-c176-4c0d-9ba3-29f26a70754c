﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="ActiveInfoSSO.Default" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title></title>
</head>
<body>
    <form id="form1" runat="server">
        <div>
            <dl>
                <dt>IsAuthenticated</dt>
                <dd><%= HttpContext.Current.User.Identity.IsAuthenticated %></dd>
                <dt>AuthenticationType</dt>
                <dd><%= HttpContext.Current.User.Identity.AuthenticationType %></dd>
                <dt>Name</dt>
                <dd><%= HttpContext.Current.User.Identity.Name %></dd>
                <dt>Is in "group1"</dt>
                <dd><%= HttpContext.Current.User.IsInRole("yourgroup1here") %></dd>
                <dt>Is in "group2"</dt>
                <dd><%= HttpContext.Current.User.IsInRole("yourgroup2here") %></dd>
            </dl>
        </div>
    </form>
</body>
</html>
