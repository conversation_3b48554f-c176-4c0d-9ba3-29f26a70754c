﻿using ActiveInfoSSO.Middlewares;
using ActiveInfoSSO.Middlewares.XForwarded;

using Owin;

using System;

namespace ActiveInfoSSO.Extensions;

public static class AppBuilderExtensions
{
    public static IAppBuilder UseForwardHeaders(this IAppBuilder builder)
    {
        if (builder == null)
            throw new ArgumentNullException(nameof(builder));

        return builder.Use<ForwardedHeadersMiddleware>();
    }

    public static IAppBuilder UseForwardHeaders(this IAppBuilder builder, ForwardedHeadersOptions options)
    {
        if (builder == null)
            throw new ArgumentNullException(nameof(builder));

        return builder.Use<ForwardedHeadersMiddleware>(options);
    }

    public static IAppBuilder UseGlobalExceptions(this IAppBuilder builder)
    {
        if (builder == null)
            throw new ArgumentNullException(nameof(builder));

        return builder.Use<GlobalExceptionMiddleware>();
    }

    public static IAppBuilder UseSignoutMiddleware(this IAppBuilder builder)
    {
        if (builder == null)
            throw new ArgumentNullException(nameof(builder));

        return builder.Use<SignoutMiddleware>();
    }

    public static IAppBuilder UseRewriterMiddleware(this IAppBuilder builder)
    {
        if (builder == null)
            throw new ArgumentNullException(nameof(builder));

        return builder.Use<UrlRewriterMiddleware>();
    }
}