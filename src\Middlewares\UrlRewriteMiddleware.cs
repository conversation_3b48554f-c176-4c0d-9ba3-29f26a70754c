﻿using log4net;

using Microsoft.Owin;
using System.Configuration;
using System.Threading.Tasks;
using System.Web;

namespace ActiveInfoSSO.Middlewares;

public class UrlRewriterMiddleware(OwinMiddleware next) : OwinMiddleware(next)
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(UrlRewriterMiddleware));

    private static readonly PathString ContentAdfsOauthSegments = PathString.FromUriComponent("/adfs/oauth2/authorize/");

    public override Task Invoke(IOwinContext context)
    {
        if (!context.Request.Path.Value.Contains($"{ContentAdfsOauthSegments}") ||
            !context.Request.QueryString.HasValue) return Next.Invoke(context);

        var redUri = HttpUtility.HtmlEncode(ConfigurationManager.AppSettings["ida:RedirectUri"]);
                
        Log.DebugFormat("Url rewrite rules - from: {0}", context.Request.Uri);
                
        context.Request.QueryString = context.Request.QueryString.Value != string.Empty
            ? new QueryString($"{context.Request.QueryString.Value}&redirect_uri={redUri}")
            : new QueryString($"redirect_uri={redUri}");

        Log.DebugFormat("Url rewrite rules - to: {0}", context.Request.Uri);

        return Next.Invoke(context);
    }
}