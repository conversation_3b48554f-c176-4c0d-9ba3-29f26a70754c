using System.ComponentModel;
using System.Globalization;
using System.Resources;

namespace ActiveInfoSSO.Handlers;

internal class TraceMessages
{
    private static ResourceManager resourceMan;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    private static ResourceManager ResourceManager => resourceMan ?? (resourceMan = new ResourceManager("ActiveInfoSSO.Handlers.TraceMessages", typeof(TraceMessages).Assembly));

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture { get; set; }

    internal static string ArtifactCreated => ResourceManager.GetString(nameof(ArtifactCreated), Culture);

    internal static string ArtifactRedirectReceived => ResourceManager.GetString(nameof(ArtifactRedirectReceived), Culture);

    internal static string ArtifactResolved => ResourceManager.GetString(nameof(ArtifactResolved), Culture);

    internal static string ArtifactResolveForKnownIdentityProvider => ResourceManager.GetString(nameof(ArtifactResolveForKnownIdentityProvider), Culture);

    internal static string ArtifactResolveReceived => ResourceManager.GetString(nameof(ArtifactResolveReceived), Culture);

    internal static string ArtifactResolveResponseSent => ResourceManager.GetString(nameof(ArtifactResolveResponseSent), Culture);

    internal static string ArtifactResponseReceived => ResourceManager.GetString(nameof(ArtifactResponseReceived), Culture);

    internal static string AssertionFound => ResourceManager.GetString(nameof(AssertionFound), Culture);

    internal static string AssertionParse => ResourceManager.GetString(nameof(AssertionParse), Culture);

    internal static string AssertionParsed => ResourceManager.GetString(nameof(AssertionParsed), Culture);

    internal static string AssertionPrehandlerCalled => ResourceManager.GetString(nameof(AssertionPrehandlerCalled), Culture);

    internal static string AssertionProcessing => ResourceManager.GetString(nameof(AssertionProcessing), Culture);

    internal static string AttrQueryAssertionReceived => ResourceManager.GetString(nameof(AttrQueryAssertionReceived), Culture);

    internal static string AttrQuerySent => ResourceManager.GetString(nameof(AttrQuerySent), Culture);

    internal static string AudienceRestrictionValidated => ResourceManager.GetString(nameof(AudienceRestrictionValidated), Culture);

    internal static string AuthnRequestPrepared => ResourceManager.GetString(nameof(AuthnRequestPrepared), Culture);

    internal static string AuthnRequestSent => ResourceManager.GetString(nameof(AuthnRequestSent), Culture);

    internal static string CommonDomainCookieReceived => ResourceManager.GetString(nameof(CommonDomainCookieReceived), Culture);

    internal static string CommonDomainCookieRedirect => ResourceManager.GetString(nameof(CommonDomainCookieRedirect), Culture);

    internal static string CommonDomainCookieRedirectForDiscovery => ResourceManager.GetString(nameof(CommonDomainCookieRedirectForDiscovery), Culture);

    internal static string CommonDomainCookieRedirectNotFound => ResourceManager.GetString(nameof(CommonDomainCookieRedirectNotFound), Culture);

    internal static string EncryptedAssertionDecrypted => ResourceManager.GetString(nameof(EncryptedAssertionDecrypted), Culture);
        
    internal static string EncryptedAssertionDecrypting => ResourceManager.GetString(nameof(EncryptedAssertionDecrypting), Culture);

    internal static string EncryptedAssertionFound => ResourceManager.GetString(nameof(EncryptedAssertionFound), Culture);
        
    internal static string IdentityProviderRedirect => ResourceManager.GetString(nameof(IdentityProviderRedirect), Culture);

    internal static string IdentityProviderRetreivedFromCommonDomainCookie => ResourceManager.GetString(nameof(IdentityProviderRetreivedFromCommonDomainCookie), Culture);

    internal static string IdentityProviderRetreivedFromDefault => ResourceManager.GetString(nameof(IdentityProviderRetreivedFromDefault), Culture);

    internal static string IdentityProviderRetreivedFromQueryString => ResourceManager.GetString(nameof(IdentityProviderRetreivedFromQueryString), Culture);

    internal static string IdentityProviderRetreivedFromSelection => ResourceManager.GetString(nameof(IdentityProviderRetreivedFromSelection), Culture);

    internal static string LogoutActionsExecuting => ResourceManager.GetString(nameof(LogoutActionsExecuting), Culture);

    internal static string LogoutHandlerCalled => ResourceManager.GetString(nameof(LogoutHandlerCalled), Culture);

    internal static string LogoutRequestParsed => ResourceManager.GetString(nameof(LogoutRequestParsed), Culture);

    internal static string LogoutRequestPostBindingParse => ResourceManager.GetString(nameof(LogoutRequestPostBindingParse), Culture);

    internal static string LogoutRequestReceived => ResourceManager.GetString(nameof(LogoutRequestReceived), Culture);

    internal static string LogoutRequestRedirectBindingParse => ResourceManager.GetString(nameof(LogoutRequestRedirectBindingParse), Culture);

    internal static string LogoutRequestSent => ResourceManager.GetString(nameof(LogoutRequestSent), Culture);

    internal static string LogoutResponseParsed => ResourceManager.GetString(nameof(LogoutResponseParsed), Culture);

    internal static string LogoutResponsePostBindingParse => ResourceManager.GetString(nameof(LogoutResponsePostBindingParse), Culture);

    internal static string LogoutResponseReceived => ResourceManager.GetString(nameof(LogoutResponseReceived), Culture);

    internal static string LogoutResponseRedirectBindingParse => ResourceManager.GetString(nameof(LogoutResponseRedirectBindingParse), Culture);

    internal static string LogoutResponseSent => ResourceManager.GetString(nameof(LogoutResponseSent), Culture);

    internal static string MetadataDocumentBeingCreated => ResourceManager.GetString(nameof(MetadataDocumentBeingCreated), Culture);

    internal static string MetadataDocumentCreated => ResourceManager.GetString(nameof(MetadataDocumentCreated), Culture);

    internal static string ReplaceAttackCheckCleared => ResourceManager.GetString(nameof(ReplaceAttackCheckCleared), Culture);

    internal static string ReplayAttackCheck => ResourceManager.GetString(nameof(ReplayAttackCheck), Culture);

    internal static string SamlResponseDecoded => ResourceManager.GetString(nameof(SamlResponseDecoded), Culture);

    internal static string SamlResponseDecoding => ResourceManager.GetString(nameof(SamlResponseDecoding), Culture);

    internal static string SamlResponseReceived => ResourceManager.GetString(nameof(SamlResponseReceived), Culture);

    internal static string SignOnActionsExecuting => ResourceManager.GetString(nameof(SignOnActionsExecuting), Culture);

    internal static string SignOnHandlerCalled => ResourceManager.GetString(nameof(SignOnHandlerCalled), Culture);

    internal static string SignOnProcessed => ResourceManager.GetString(nameof(SignOnProcessed), Culture);

    internal static string SOAPMessageParse => ResourceManager.GetString(nameof(SOAPMessageParse), Culture);
}