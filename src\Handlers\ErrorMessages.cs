using System.ComponentModel;
using System.Globalization;
using System.Resources;

namespace ActiveInfoSSO.Handlers;

internal static class ErrorMessages
{
    private static ResourceManager _resourceMan;

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static ResourceManager ResourceManager => _resourceMan ??= new ResourceManager("ActiveInfoSSO.Handlers.ErrorMessages", typeof(ErrorMessages).Assembly);

    [EditorBrowsable(EditorBrowsableState.Advanced)]
    internal static CultureInfo Culture { get; set; }

    internal static string ArtifactResolveIdentityProviderUnknown =>
        ResourceManager.GetString(nameof(ArtifactResolveIdentityProviderUnknown), Culture);

    internal static string ArtifactResolveSignatureInvalid =>
        ResourceManager.GetString(nameof(ArtifactResolveSignatureInvalid), Culture);

    internal static string ArtifactResponseMissingAssertion =>
        ResourceManager.GetString(nameof(ArtifactResponseMissingAssertion), Culture);

    internal static string ArtifactResponseMissingResponse =>
        ResourceManager.GetString(nameof(ArtifactResponseMissingResponse), Culture);

    internal static string ArtifactResponseSignatureInvalid =>
        ResourceManager.GetString(nameof(ArtifactResponseSignatureInvalid), Culture);

    internal static string ArtifactResponseStatusCodeInvalid =>
        ResourceManager.GetString(nameof(ArtifactResponseStatusCodeInvalid), Culture);

    internal static string AssertionExpired => ResourceManager.GetString(nameof(AssertionExpired), Culture);

    internal static string AssertionIdentityProviderUnknown =>
        ResourceManager.GetString(nameof(AssertionIdentityProviderUnknown), Culture);

    internal static string AssertionNotFound => ResourceManager.GetString(nameof(AssertionNotFound), Culture);

    internal static string AssertionOneTimeUseExceeded =>
        ResourceManager.GetString(nameof(AssertionOneTimeUseExceeded), Culture);

    internal static string AssertionSignatureInvalid =>
        ResourceManager.GetString(nameof(AssertionSignatureInvalid), Culture);

    internal static string AttrQueryNoLogin => ResourceManager.GetString(nameof(AttrQueryNoLogin), Culture);

    internal static string AttrQueryStatusNotSuccessful =>
        ResourceManager.GetString(nameof(AttrQueryStatusNotSuccessful), Culture);

    internal static string CertificateIsNotRfc3280Valid =>
        ResourceManager.GetString(nameof(CertificateIsNotRfc3280Valid), Culture);

    internal static string CertificateNotFound => ResourceManager.GetString(nameof(CertificateNotFound), Culture);

    internal static string CertificateNotUnique => ResourceManager.GetString(nameof(CertificateNotUnique), Culture);

    internal static string CommonDomainCookieIdentityProviderInvalid =>
        ResourceManager.GetString(nameof(CommonDomainCookieIdentityProviderInvalid), Culture);

    internal static string ConfigMissingIdentityProvidersElement =>
        ResourceManager.GetString(nameof(ConfigMissingIdentityProvidersElement), Culture);

    internal static string ConfigMissingMetadataLocation =>
        ResourceManager.GetString(nameof(ConfigMissingMetadataLocation), Culture);

    internal static string ConfigMissingSaml2Element =>
        ResourceManager.GetString(nameof(ConfigMissingSaml2Element), Culture);

    internal static string ConfigMissingServiceProviderElement =>
        ResourceManager.GetString(nameof(ConfigMissingServiceProviderElement), Culture);

    internal static string ConfigMissingServiceProviderIdAttribute =>
        ResourceManager.GetString(nameof(ConfigMissingServiceProviderIdAttribute), Culture);

    internal static string ConfigMissingSigningCertificateElement =>
        ResourceManager.GetString(nameof(ConfigMissingSigningCertificateElement), Culture);

    internal static string ConfigNotInitialized => ResourceManager.GetString(nameof(ConfigNotInitialized), Culture);

    internal static string ConfigServiceProviderMissingSignOnEndpoint =>
        ResourceManager.GetString(nameof(ConfigServiceProviderMissingSignOnEndpoint), Culture);

    internal static string ConfigSigningCertificateMissingPrivateKey =>
        ResourceManager.GetString(nameof(ConfigSigningCertificateMissingPrivateKey), Culture);

    internal static string EndpointBindingInvalid =>
        ResourceManager.GetString(nameof(EndpointBindingInvalid), Culture);

    internal static string ExpectedInResponseToEmpty =>
        ResourceManager.GetString(nameof(ExpectedInResponseToEmpty), Culture);

    internal static string ExpectedInResponseToMissing =>
        ResourceManager.GetString(nameof(ExpectedInResponseToMissing), Culture);

    internal static string GenericError => ResourceManager.GetString(nameof(GenericError), Culture);

    internal static string MetadataLocationNotFound =>
        ResourceManager.GetString(nameof(MetadataLocationNotFound), Culture);

    internal static string MetadataSignQueryParameterInvalid =>
        ResourceManager.GetString(nameof(MetadataSignQueryParameterInvalid), Culture);

    internal static string ReplayAttack => ResourceManager.GetString(nameof(ReplayAttack), Culture);

    internal static string RequestSignatureInvalid =>
        ResourceManager.GetString(nameof(RequestSignatureInvalid), Culture);

    internal static string RequestSignatureMissing =>
        ResourceManager.GetString(nameof(RequestSignatureMissing), Culture);

    internal static string ResponseMissingInResponseToAttribute =>
        ResourceManager.GetString(nameof(ResponseMissingInResponseToAttribute), Culture);

    internal static string ResponseSignatureInvalid =>
        ResourceManager.GetString(nameof(ResponseSignatureInvalid), Culture);

    internal static string ResponseSignatureMissing =>
        ResourceManager.GetString(nameof(ResponseSignatureMissing), Culture);

    internal static string ResponseStatusIsNoPassive =>
        ResourceManager.GetString(nameof(ResponseStatusIsNoPassive), Culture);

    internal static string ResponseStatusNotSuccessful =>
        ResourceManager.GetString(nameof(ResponseStatusNotSuccessful), Culture);

    internal static string SoapMessageUnsupportedSamlMessage =>
        ResourceManager.GetString(nameof(SoapMessageUnsupportedSamlMessage), Culture);

    internal static string UnauthenticatedAccess =>
        ResourceManager.GetString(nameof(UnauthenticatedAccess), Culture);

    internal static string UnknownEncoding => ResourceManager.GetString(nameof(UnknownEncoding), Culture);

    internal static string UnknownIdentityProvider =>
        ResourceManager.GetString(nameof(UnknownIdentityProvider), Culture);

    internal static string UnsupportedRequestType =>
        ResourceManager.GetString(nameof(UnsupportedRequestType), Culture);
}