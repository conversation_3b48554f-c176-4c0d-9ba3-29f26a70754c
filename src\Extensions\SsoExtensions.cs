﻿using ActiveInfoSSO.Utils;

using IdentityModel.Client;

using log4net;

using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;

using Okta.AspNet;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Net.Http;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Owin.Host.SystemWeb;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.OpenIdConnect;
using Owin;

namespace ActiveInfoSSO.Extensions;

public static class SsoExtensions
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(SsoExtensions));

    public static string Md5ToHash64(this string inputString)
    {
        var hash = MD5.Create();
        var inputBytes = Encoding.Default.GetBytes(inputString);
        var outputBytes = hash.ComputeHash(inputBytes);
        return HttpUtility.HtmlEncode(Convert.ToBase64String(outputBytes));
    }

    public static string EnsureTrailingSlash(this string value)
    {
        if (!value?.EndsWith("/", StringComparison.Ordinal) ?? true)
        {
            return value + "/";
        }

        return value;
    }

    public static IAppBuilder SetOpenIdAuthOptions(this IAppBuilder app, bool openId, string opAuth, string azAuth, string clientId, string clientSecret, string redirectUri, string postLogoutUri, string claimName = "Username", bool implicitFlow = false)
    {
        var authority = openId ? opAuth : azAuth;

        var implicitOrHybrid = implicitFlow
            ? new OpenIdConnectAuthenticationOptions
            {
                RedirectUri = redirectUri,
                PostLogoutRedirectUri = postLogoutUri,
                Scope = OpenIdConnectScope.OpenIdProfile,
                ResponseType = OpenIdConnectResponseType.IdToken,
                ResponseMode = OpenIdConnectResponseMode.FormPost,
                TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = claimName,
                    ValidateIssuer = false,
                    LogValidationExceptions = true,
                    RoleClaimType = "role"
                }
            }
            : new OpenIdConnectAuthenticationOptions()
            {
                RedirectUri = redirectUri,
                PostLogoutRedirectUri = postLogoutUri,
                ResponseType = OpenIdConnectResponseType.Code,
                ClientSecret = clientSecret,
                ResponseMode = OpenIdConnectResponseMode.Query,
                Scope = OpenIdConnectScope.OpenIdProfile,
                TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = false,
                    NameClaimType = claimName,
                    RoleClaimType = "role"
                },
                RedeemCode = true
            };
        
        var oidOption = openId
            ? new OpenIdConnectAuthenticationOptions
            {
                AuthenticationMode = AuthenticationMode.Active,
                ClientSecret = clientSecret,
                ResponseType = OpenIdConnectResponseType.Code,
                RedeemCode = true,
                RedirectUri = redirectUri,
                Scope = OpenIdConnectScope.OpenId,
                TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = claimName
                }
            }
            : implicitOrHybrid;

        oidOption.Authority = authority;
        oidOption.ClientId = clientId;
        oidOption.CookieManager = new SameSiteCookieManager(new SystemWebCookieManager());
        oidOption.Notifications = new OpenIdConnectAuthenticationNotifications
        {
            AuthenticationFailed = context =>
            {
                if (context.Exception is OpenIdConnectProtocolInvalidNonceException || context.Exception.Message.Contains("IDX21323"))
                {
                    context.HandleResponse();
                    context.SkipToNextMiddleware();
                    return Task.FromResult(0);
                }

                Log.DebugFormat("AuthenticationFailed - {0}", context.Exception.Message);
                return Task.FromResult(0);
            },
            AuthorizationCodeReceived = context =>
            {
                Log.InfoFormat("AuthorizationCodeReceived: {0}", context.Code);

                return Task.FromResult(0);
            },
            MessageReceived = context =>
            {
                Log.InfoFormat("MessageReceived - Nonce: {0}", context.ProtocolMessage.Nonce);
                return Task.FromResult(0);
            },
            RedirectToIdentityProvider = context =>
            {
                Log.InfoFormat("RedirectToIdentityProvider - context.ProtocolMessage.RequestType => {0}", context.ProtocolMessage.RequestType);

                if (context.ProtocolMessage.RequestType != OpenIdConnectRequestType.Logout)
                    return Task.FromResult(0);

                var ci = (ClaimsIdentity)ClaimsPrincipal.Current.Identity;

                var token = ((System.IdentityModel.Tokens.BootstrapContext)ci.BootstrapContext)?.Token ?? context.OwinContext.Authentication?.User?.Claims.FirstOrDefault(x => x.Type == "id_token")?.Value;

                var idTokenHint = token ?? context.Request.Cookies["stored_id_token"] ?? context.ProtocolMessage.IdToken;

                if (idTokenHint == null) return Task.FromResult(0);

                context.ProtocolMessage.IdTokenHint = idTokenHint;

                var signOutMessageId = context.OwinContext.Request.Query.Get("id");

                if (signOutMessageId != null)
                    context.OwinContext.Response.Cookies.Append("stored_sign_out_message", signOutMessageId);

                return Task.FromResult(0);
            },
            SecurityTokenReceived = context =>
            {
                Log.InfoFormat("SecurityTokenReceived - Nonce: {0}", context.ProtocolMessage.Nonce);
                return Task.FromResult(0);
            },
            SecurityTokenValidated = async context =>
            {
                if (openId)
                {
                    context.AuthenticationTicket.Identity.AddClaim(new Claim("id_token", context.ProtocolMessage.IdToken));
                    context.AuthenticationTicket.Identity.AddClaim(new Claim("access_token", context.ProtocolMessage.AccessToken));

                    context.OwinContext.Response.Cookies.Append("stored_id_token", context.ProtocolMessage.IdToken);

                    var client = new HttpClient();

                    var response = await client.GetUserInfoAsync(new UserInfoRequest
                    {
                        Address = $"{authority}/userinfo",
                        Token = context.ProtocolMessage.AccessToken
                    });

                    if (response.IsError) throw new InvalidOperationException(response.Error);

                    context.AuthenticationTicket.Identity.AddClaims(response.Claims);
                    context.AuthenticationTicket.Identity.AddClaim(new Claim("Username", response.Claims.FirstOrDefault(c => c.Type.Equals("Username"))?.Value));
                }

                Log.Info("SecurityTokenValidated - Success");
                var claims = context.AuthenticationTicket.Identity.Claims;
                var groups = from c in claims
                    where c.Type == "groups"
                    select c;

                foreach (var group in groups)
                    context.AuthenticationTicket.Identity.AddClaim(new Claim(ClaimTypes.Role, group.Value));
            }
        };

        app.UseOpenIdConnectAuthentication(oidOption);

        return app;
    }

    public static IAppBuilder AddOktaAuthenticationMiddleware(this IAppBuilder app)
    {
        return app.UseOktaMvc(new OktaMvcOptions
        {
            OktaDomain = ConfigurationManager.AppSettings["okta:OktaDomain"],
            ClientId = ConfigurationManager.AppSettings["okta:ClientId"],
            ClientSecret = ConfigurationManager.AppSettings["okta:ClientSecret"],
            RedirectUri = ConfigurationManager.AppSettings["okta:RedirectUri"],
            PostLogoutRedirectUri = ConfigurationManager.AppSettings["okta:PostLogoutRedirectUri"],
            GetClaimsFromUserInfoEndpoint = true,
            Scope = new List<string> { "openid", "profile", "email" },
        });
    }
}