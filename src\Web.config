﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net" />
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="ActiveInfoSSO.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
    <!-- Commenta/decommenta per abilitare l'auth SAML2 -->
    <section name="saml2" type="SAML2.Config.Saml2Section, SAML2" />
  </configSections>
  <log4net debug="false">
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="[SimpleModuleConfig] %d [%t] %-5p %c - %m%n" />
      </layout>
    </appender>
    <appender name="ColoredConsoleAppender" type="log4net.Appender.ColoredConsoleAppender">
      <mapping>
        <level value="WARN" />
        <backColor value="Yellow" />
      </mapping>
      <mapping>
        <level value="INFO" />
        <foreColor value="White" />
        <backColor value="Blue" />
      </mapping>
      <mapping>
        <level value="DEBUG" />
        <foreColor value="White" />
        <backColor value="Green" />
      </mapping>
      <mapping>
        <level value="ERROR" />
        <foreColor value="White" />
        <backColor value="Red, HighIntensity" />
      </mapping>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger:%M:%L [%ndc] - %message%newline" />
      </layout>
    </appender>
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="Log\logfile.txt" />
      <appendToFile value="true" />
      <rollingStyle value="Composite" />
      <datePattern value="yyyyMMdd" />
      <maxSizeRollBackups value="50" />
      <maximumFileSize value="10MB" />
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%ndc] - %message%newline" />
      </layout>
    </appender>
    <root>
      <level value="ERROR" />
      <appender-ref ref="RollingLogFileAppender" />
      <appender-ref ref="ColoredConsoleAppender" />
    </root>
  </log4net>
  <system.web>
    <machineKey
      validationKey="83BAFC318C3C5376A79739FCA476FE044EACC5A324C0E4D685F2966E6E9ADAEA"
      decryptionKey="4241B31B99B8AE09FD2DAEBA27880B0C"
      validation="SHA1"
      decryption="AES" />
    <httpRuntime maxQueryStringLength="2097151" maxRequestLength="2097151" targetFramework="4.7.2" />
    <globalization culture="IT-it" uiCulture="IT-it" requestEncoding="utf-8" responseEncoding="utf-8" />
    <compilation debug="true" targetFramework="4.7.2" />
    <authentication mode="None" />
    <authorization>
      <allow users="*" />
      <deny users="?" />
    </authorization>
    <pages controlRenderingCompatibilityVersion="4.0" />
  </system.web>
  <system.webServer>
    <httpProtocol>
      <customHeaders>
        <add name="Content-Security-Policy" value="script-src 'unsafe-inline'" />
      </customHeaders>
    </httpProtocol>
    <handlers>
      <remove name="SAML2.Protocol.Saml20SignonHandler" />
      <remove name="SAML2.Protocol.Saml20LogoutHandler" />
      <remove name="SAML2.Protocol.Saml20MetadataHandler" />
      <add name="SAML2.Protocol.Saml20SignonHandler" verb="*" path="Login.ashx" type="ActiveInfoSSO.Handlers.Saml20SignonHandler, ActiveInfoSSO" />
      <add name="SAML2.Protocol.Saml20LogoutHandler" verb="*" path="Logout.ashx" type="SAML2.Protocol.Saml20LogoutHandler, SAML2" />
      <add name="SAML2.Protocol.Saml20MetadataHandler" verb="*" path="Metadata.ashx" type="SAML2.Protocol.Saml20MetadataHandler, SAML2" />
    </handlers>
    <modules runAllManagedModulesForAllRequests="true">
      <remove name="FormsAuthenticationModule" />
    </modules>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="**********" maxUrl="**********" maxQueryString="**********" />
      </requestFiltering>
    </security>
  </system.webServer>
  <!-- SAML2 -->
  <!-- Commenta/decommenta e modifica con le specifiche SAML2 dell'IDP di riferimento -->
  <saml2>
    <allowedAudienceUris>
      <audience uri="sso-eunote016.euroged.biz" />
    </allowedAudienceUris>
    <serviceProvider id="sso-eunote016.euroged.biz" server="https://sso-eunote016.euroged.biz:442">
      <signingCertificate findValue="97ff306169b84927006f406b81651d592bf189ec" storeLocation="LocalMachine" storeName="My" x509FindType="FindByThumbprint" />
      <endpoints>
        <endpoint localPath="Login.ashx" type="SignOn" redirectUrl="~/Default.aspx" />
        <endpoint localPath="Logout.ashx" type="Logout" redirectUrl="~/Default.aspx" />
        <endpoint localPath="Metadata.ashx" type="Metadata" />
      </endpoints>
      <nameIdFormats allowCreate="true">
        <add format="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress" />
      </nameIdFormats>
    </serviceProvider>
    <identityProviders
      metadata="C:\SorgentiDominio\Euroged_Web\ActiveInfoSSO365-Bridge\src\Metadata">
      <!-- Okta IDP -->
      <add id="eunote06.euroged.biz" default="true" allowUnsolicitedResponses="true"
        omitAssertionSignatureCheck="true">
        <endpoints>
          <endpoint type="SignOn"
            url="https://accounts.google.com/o/saml2/idp?idpid=C03ddxhnf"
            binding="Redirect" />        
        </endpoints>
        <certificateValidations>
          <add type="SAML2.Specification.SelfIssuedCertificateSpecification, SAML2" />
        </certificateValidations>
      </add>
    </identityProviders>
    <logging loggingFactory="SAML2.Logging.Log4Net.Log4NetLoggerFactory, SAML2.Logging.Log4Net" />
  </saml2>
  <!-- SAML2 -->
  <appSettings>
    <add key="AccessKey" value="S4d#?3sdfla!23984G7Y%4asg%AT3Pe2" />
    <add key="wsActiveinfo.webservice" value="https://eunote016.euroged.biz/develop/webservicecs.asmx" />
    <add key="activeinfo.mobileUrl" value="" />
    <!-- possible values (case-insensitive): openid, azure, azureimplicit, azurefederation, saml2, okta, oktasaml -->
    <add key="mode" value="okta" />

    <!-- Azure -->
    <add key="ida:ClientId" value="848a28d5-af1c-48dd-bc21-a0e28ba5b8b2" />
    <add key="ida:ClientSecret" value="****************************************" />
    <add key="ida:RedirectUri" value="https://sso-eunote016.euroged.biz:442/Default.aspx?mobile=true" />
    <add key="ida:PostLogoutRedirectUri" value="https://eunote016.euroged.biz/mobile/Account/Logout" />
    <add key="ida:Domain" value="euroged.it" />
    <add key="ida:TenantId" value="481e9d24-00d1-4b6d-9e90-2ee250bb46ad" />
    <add key="ida:AADInstance" value="https://login.microsoftonline.com/{0}/v2.0" />
    <add key="ida:ClaimName" value="preferred_username" />
    <add key="ida:WsFederationMetadata" value="https://login.microsoftonline.com/{0}/federationmetadata/2007-06/federationmetadata.xml" />
    <!-- FINE Azure-->

    <!-- OpenId -->
    <add key="ido:ClientId" value="f578ee16-16f8-4e1b-90f7-c9bd19617829" />
    <add key="ido:ClientSecret" value="VUwiU31ZpQFNs-ZcnUxVtz5~Qr" />
    <add key="ido:Authority" value="https://oauth.id.jumpcloud.com" />
    <add key="ido:RedirectUri" value="https://sso-eunote016.euroged.biz:442/Default.aspx?mobile=true" />
    <add key="ido:PostLogoutRedirectUri" value="https://eunote016.euroged.biz:366/Account/Logout" />
    <add key="ido:ClaimName" value="Username" />
    <add key="ido:FixedLogoutRedirectUri" value="https://eunote016.euroged.biz:366/Account/Logout" />
    <!-- FINE OpenId -->

    <!-- SAML2 (IDP OKTA) -->
    <add key="ids:SingleLogoutServiceUri" value="" />
    <add key="ids:FixedLogoutRedirectUri" value="https://eunote016.euroged.biz/develop/login2.aspx?action=Logoutsso" />

    <!-- Okta OpenId -->
    <add key="okta:ClientId" value="0oatnafxhcaa0oDbD697" />
    <add key="okta:ClientSecret" value="1RkcPnuEhrGxNHjQ_u9KJFExvdoG-bNqbmqsLxJ_HNGWpjCeJ-nvjOCKCnHOnxgH" />
    <add key="okta:OktaDomain" value="https://integrator-9509086.okta.com" />
    <add key="okta:RedirectUri" value="https://sso-eunote016.euroged.it:442/" />
    <add key="okta:PostLogoutRedirectUri" value="https://eunote016.euroged.biz/login2.aspx?action=Logoutsso" />  
  </appSettings>
  <system.serviceModel>
    <bindings />
    <client />
  </system.serviceModel>
  <applicationSettings>
    <ActiveInfoSSO.Properties.Settings>
      <setting name="ActiveInfoSSO_wsActiveinfo_Activeinfo_WebService"
        serializeAs="String">
        <value>https://eunote016.euroged.biz/develop/webservicecs.asmx</value>
      </setting>
    </ActiveInfoSSO.Properties.Settings>
  </applicationSettings>
  <runtime>
    <AppContextSwitchOverrides value="Switch.System.ServiceModel.DisableUsingServicePointManagerSecurityProtocols=false;Switch.System.Net.DontEnableSchUseStrongCrypto=false;Switch.System.Security.Cryptography.Xml.UseInsecureHashAlgorithms=true;Switch.System.Security.Cryptography.Pkcs.UseInsecureHashAlgorithms=true" />
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.7" newVersion="9.0.0.7" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.7" newVersion="9.0.0.7" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OpenIdConnect" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.3.0" newVersion="4.2.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Protocols.OpenIdConnect" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Protocols" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.3.0" newVersion="6.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="IdentityModel" publicKeyToken="e7877f4675df049f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.JsonWebTokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Protocols.WsFederation" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens.Saml" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.13.0.0" newVersion="8.13.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.4.0" newVersion="4.2.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.4" newVersion="9.0.0.4" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.6.0" newVersion="4.1.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.7" newVersion="9.0.0.7" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.7" newVersion="9.0.0.7" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.TimeProvider" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.7" newVersion="9.0.0.7" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.4" newVersion="9.0.0.4" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>