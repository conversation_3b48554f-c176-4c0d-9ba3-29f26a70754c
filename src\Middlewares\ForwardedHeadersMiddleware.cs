﻿using ActiveInfoSSO.Extensions;
using ActiveInfoSSO.Middlewares.XForwarded;

using log4net;

using Microsoft.Owin;

using System;
using System.Linq;
using System.Threading.Tasks;

namespace ActiveInfoSSO.Middlewares;

/// <summary>
/// Owin Middleware that can be used to handle requests forwarded by load balancers.
/// Code originally taken from StoryHub
/// Handles X-Forwarded-Proto, X-Forwarded-Port 
/// </summary>
public class ForwardedHeadersMiddleware(OwinMiddleware next, ForwardedHeadersOptions options = default) : OwinMiddleware(next)
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(ForwardedHeadersMiddleware));

    private const string ForwardedHeaderName = "Forwarded";
    private const string XForwardedHeaderPrefix = "X-Forward";

    public override Task Invoke(IOwinContext context)
    {
        var request = context.Request;

        if (options.PreferredStandard != ForwardHeaderStandard.Unspecified)
        {
            ProcessForwardHeaders(context, request, options.PreferredStandard);
        }
        else
        {
            var forwardedHeaderStandard = GetForwardedHeaderStandard(context.Request.Headers);

            if (forwardedHeaderStandard == ForwardHeaderStandard.Unspecified)
                return Next.Invoke(context);

            ProcessForwardHeaders(context, request, forwardedHeaderStandard);
        }

        return Next.Invoke(context);
    }

    private void ProcessForwardHeaders(IOwinContext context, IOwinRequest request, ForwardHeaderStandard forwardedHeaderStandard)
    {
        switch (forwardedHeaderStandard)
        {
            case ForwardHeaderStandard.Forwarded:
            {
                var forwardedHeaders = new ForwardedHeaders(request, options);
                ProcessForwardedHeaders(forwardedHeaders, context, request);
                break;
            }
            case ForwardHeaderStandard.Xforwarded:
            {
                var forwardedHeaders = new XForwardedHeaders(request, options);
                ProcessForwardedHeaders(forwardedHeaders, context, request);
                break;
            }
            default:
                throw new NotImplementedException($"Processing of {forwardedHeaderStandard} is not supported/implemented");
        }
    }

    private static void ProcessForwardedHeaders(IForwardedHeaders headers, IOwinContext context, IOwinRequest request)
    {
        var updatedHeaders = headers.ApplyForwardedHeaders(request);
        context.UpdateRequestHeaders(updatedHeaders);
        foreach (var header in updatedHeaders)
        {
            Log.DebugFormat("Request header {0} = {1}", header.Key, string.Join(";", header.Value));
        }
    }

    private static ForwardHeaderStandard GetForwardedHeaderStandard(IHeaderDictionary headers)
    {
        /* INFO: Usually there are two standards we are expecting here.
         * Either the X-Forwarded-* headers or the Forwarded header
         * defined in RFC-7239 here: https://tools.ietf.org/pdf/rfc7239.pdf.
         *
         * Giving precedence to the RFC here, if we see that we have Forwarded
         * header set here, we would definitely pick that one over the X-Forwarded one.
         */

        switch (string.IsNullOrWhiteSpace(headers[ForwardedHeaderName]))
        {
            case false:
                return ForwardHeaderStandard.Forwarded;
            default:
                return headers.Keys.Any(x => x.StartsWith(XForwardedHeaderPrefix) && !string.IsNullOrWhiteSpace(headers[x])) ? ForwardHeaderStandard.Xforwarded : ForwardHeaderStandard.Unspecified;
        }
    }
}