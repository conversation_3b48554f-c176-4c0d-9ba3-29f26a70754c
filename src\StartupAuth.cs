﻿using ActiveInfoSSO.Extensions;
using ActiveInfoSSO.Middlewares.XForwarded;
using ActiveInfoSSO.Utils;

using log4net;

using Microsoft.IdentityModel.Logging;
using Microsoft.IdentityModel.Tokens;
using Microsoft.Owin.Extensions;
using Microsoft.Owin.Infrastructure;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security.WsFederation;

using Owin;

using System;
using System.Configuration;
using System.IdentityModel.Claims;
using System.Net;
using System.Threading.Tasks;
#pragma warning disable S4830

namespace ActiveInfoSSO;

public partial class Startup
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(Startup));

    private static readonly string AadInstance = ConfigurationManager.AppSettings["ida:AADInstance"].EnsureTrailingSlash();
    private static readonly string TenantId = ConfigurationManager.AppSettings["ida:TenantId"];
    private static readonly string OpAuth = ConfigurationManager.AppSettings["ido:Authority"];
    private static readonly string Authority = string.Format(AadInstance, TenantId);
    private static readonly string WsFederationMetadata = string.Format(ConfigurationManager.AppSettings["ida:WsFederationMetadata"], TenantId);

    public static readonly AuthType Auth = Enum.TryParse<AuthType>(ConfigurationManager.AppSettings["mode"], true, out var res) ? res : AuthType.Azure;

    public void ConfigureAuth(IAppBuilder app)
    {
        ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            
        ServicePointManager.ServerCertificateValidationCallback += (_, _, _, _) => true;

        IdentityModelEventSource.ShowPII = true;

        app.SetDefaultSignInAsAuthenticationType(Auth is AuthType.AzureFederation
            ? WsFederationAuthenticationDefaults.AuthenticationType
            : CookieAuthenticationDefaults.AuthenticationType);

        app.UseCookieAuthentication(new CookieAuthenticationOptions());

        app.UseGlobalExceptions();

        switch (Auth)
        {
            case AuthType.AzureImplicit:
            case AuthType.Azure:
                app.SetOpenIdAuthOptions(false,
                    null,
                    Authority,
                    ConfigurationManager.AppSettings["ida:ClientId"],
                    ConfigurationManager.AppSettings["ida:ClientSecret"],
                    ConfigurationManager.AppSettings["ida:RedirectUri"],
                    ConfigurationManager.AppSettings["ida:PostLogoutRedirectUri"],
                    ConfigurationManager.AppSettings["ida:ClaimName"],
                    Auth == AuthType.AzureImplicit);
                break;
            case AuthType.OpenId:
                app.SetOpenIdAuthOptions(true,
                    OpAuth,
                    null,
                    ConfigurationManager.AppSettings["ido:ClientId"],
                    ConfigurationManager.AppSettings["ido:ClientSecret"],
                    ConfigurationManager.AppSettings["ido:RedirectUri"],
                    ConfigurationManager.AppSettings["ido:PostLogoutRedirectUri"],
                    ConfigurationManager.AppSettings["ido:ClaimName"]);
                break;
            case AuthType.AzureFederation:
                app.UseWsFederationAuthentication(
                    new WsFederationAuthenticationOptions
                    {
                        MetadataAddress = WsFederationMetadata,
                        Wtrealm = ConfigurationManager.AppSettings["ida:RedirectUri"],
                        AuthenticationType = WsFederationAuthenticationDefaults.AuthenticationType,
                        AuthenticationMode = AuthenticationMode.Passive,
                        SignInAsAuthenticationType = CookieAuthenticationDefaults.AuthenticationType,
                        Wreply = ConfigurationManager.AppSettings["ida:RedirectUri"],
                        SignOutWreply = ConfigurationManager.AppSettings["ida:PostLogoutRedirectUri"],
                        TokenValidationParameters = new TokenValidationParameters
                        {
                            NameClaimType = ConfigurationManager.AppSettings["ida:ClaimName"],
                            ValidateIssuer = false,
                            LogValidationExceptions = true,
                            RoleClaimType = "role"
                        },
                        Notifications = new WsFederationAuthenticationNotifications()
                        {
                            AuthenticationFailed = notification =>
                            {
                                Log.DebugFormat("AuthenticationFailed - Message: {0} - State: {1} - Protocol Message: {2}", notification.Exception.Message, notification.State, notification.ProtocolMessage);
                                notification.HandleResponse();
                                notification.Response.Redirect("/?errormessage=" + notification.Exception.Message);
                                return Task.CompletedTask;
                            },
                            MessageReceived = notification =>
                            {
                                Log.DebugFormat("MessageReceived - State: {0} - Protocol Message: {1}", notification.State, notification.ProtocolMessage);
                                return Task.CompletedTask;
                            },
                            RedirectToIdentityProvider = notification =>
                            {
                                Log.DebugFormat("RedirectToIdentityProvider - State: {0} - Protocol Message: {1}", notification.State, notification.ProtocolMessage);
                                return Task.CompletedTask;
                            },
                            SecurityTokenReceived = notification =>
                            {
                                Log.DebugFormat("SecurityTokenReceived - State: {0} - Protocol Message: {1}", notification.State, notification.ProtocolMessage);
                                return Task.CompletedTask;
                            },
                            SecurityTokenValidated = notification =>
                            {
                                Log.DebugFormat("SecurityTokenValidated - Claims Name {2} - State: {0} - Protocol Message: {1}", notification.State, notification.ProtocolMessage, notification.AuthenticationTicket.Identity.Name);

                                var identity = notification.AuthenticationTicket.Identity;
                                var defaultName = identity.FindFirst(claim => claim.Type == ClaimTypes.NameIdentifier);

                                Log.DebugFormat("SecurityTokenValidated - Default Name {0}", defaultName);

                                // Forcefully set a cookie so that the WsFed provider does not have to be consulted on every request as long as this cookie is valid

                                var ticket = new AuthenticationTicket(notification.AuthenticationTicket.Identity, notification.AuthenticationTicket.Properties);
                                var currentUtc = new SystemClock().UtcNow;
                                ticket.Properties.IssuedUtc = currentUtc;
                                ticket.Properties.ExpiresUtc = currentUtc.Add(TimeSpan.FromMinutes(1));
                                notification.AuthenticationTicket = ticket;
                                notification.OwinContext.Authentication.SignIn(new AuthenticationProperties(), notification.AuthenticationTicket.Identity);

                                // context.Authentication.User.Identity.IsAuthenticated will now be true

                                return Task.FromResult(notification);
                            }
                        }
                    });

                app.UseRewriterMiddleware();
                break;
            case AuthType.Saml2:
            case AuthType.OktaSaml:
                // Use http handler (SAML2 dll) challenge action through google's saml2 idp to gain the response with user authentication
                break;
            case AuthType.Okta:
                app.AddOktaAuthenticationMiddleware();
                break;
            default:
                throw new InvalidOperationException("Authentication type is null or invalid, please check your web.config settings");
        }

        // This makes any middleware defined above this line run before the Authorization rule is applied in web.config
        app.UseStageMarker(PipelineStage.Authenticate);

        app.UseForwardHeaders(new ForwardedHeadersOptions
        {
            RequireHeaderSymmetry = false,
            PreferredStandard = ForwardHeaderStandard.Xforwarded
        });

        app.UseSignoutMiddleware();

        app.UseStageMarker(PipelineStage.PostAuthenticate);
    }
}