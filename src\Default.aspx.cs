#nullable enable

using ActiveInfoSSO.Entities;
using ActiveInfoSSO.Extensions;
using ActiveInfoSSO.Handlers;
using ActiveInfoSSO.Utils;
using ActiveInfoSSO.wsActiveinfo;

using log4net;

using Okta.AspNet;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Principal;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Xml.Linq;
using System.Xml.Serialization;
using SAML2;
using SAML2.Identity;
#pragma warning disable JMA001
#pragma warning disable S3928


namespace ActiveInfoSSO;

public class Default : Page
{
    private static readonly ILog Log = LogManager.GetLogger(typeof(Default));

    private Saml20Assertion? _saml2Assertion;

    private string? _authenticatedUserName;

    private SamlResponse? _samlResponse;

    protected void Page_Load(object sender, EventArgs e)
    {
        Log.Info("Page_Load");

        if (IsPostBack)
            return;

        CheckUserAndRedirect();
    }

    private void CheckUserAndRedirect()
    {
        var mobile = !string.IsNullOrEmpty(Request.QueryString["mobile"]);

        switch (Startup.Auth)
        {
            case AuthType.AzureFederation:
            case AuthType.Azure:
            case AuthType.AzureImplicit:
            case AuthType.OpenId:
            case AuthType.Okta:
                var manageOAuthRequest = ManageOAuthRequest();
                if (manageOAuthRequest) return;
                Response.Redirect(VerifyIdentity(null, mobile));
                break;

            case AuthType.OktaSaml:
            case AuthType.Saml2:
                var manageSaml2Response = ManageSaml2Response();
                if (manageSaml2Response) return;
                Response.Redirect(VerifyIdentity(_samlResponse, mobile));
                break;

            default:
                throw new ArgumentOutOfRangeException(nameof(Startup.Auth), Startup.Auth, "Invalid authentication type");
        }

        RedirectActiveinfoLoginUrl("NotAvailable", mobile);
    }

    private void OpenIdChallenge()
    {
        Log.DebugFormat("Default.aspx Authenticazione");

        switch (Startup.Auth)
        {
            case AuthType.Saml2:
            case AuthType.OktaSaml:
                Response.Redirect("~/Login.ashx");
                return;

            case AuthType.AzureFederation:
                HttpContext.Current?.Request.GetOwinContext()?.Authentication.Challenge(
                    new Microsoft.Owin.Security.AuthenticationProperties
                    { RedirectUri = ConfigurationManager.AppSettings["ida:RedirectUri"] },
                    "WsFederation");
                break;

            case AuthType.Okta:
                HttpContext.Current.Request.GetOwinContext().Authentication.Challenge(OktaDefaults.MvcAuthenticationType);
                break;

            default:
                HttpContext.Current.Request.GetOwinContext().Authentication.Challenge();
                break;
        }

        Log.DebugFormat("Default.aspx Challenge completed");

        HttpContext.Current?.ApplicationInstance.CompleteRequest();

        Log.DebugFormat("Default.aspx Request completes");
    }

    private void Logout()
    {
        var logoutRedirectSaml = ConfigurationManager.AppSettings["ids:FixedLogoutRedirectUri"];
        var logoutRedirectOpen = ConfigurationManager.AppSettings["ido:FixedLogoutRedirectUri"];
        var postLogoutRedirectUriOkta = ConfigurationManager.AppSettings["okta:PostLogoutRedirectUri"];
        var singleLogoutServiceUri = ConfigurationManager.AppSettings["ids:SingleLogoutServiceUri"];
        var postLogoutRedirectUri = ConfigurationManager.AppSettings["ido:PostLogoutRedirectUri"];

        switch (Startup.Auth)
        {
            case AuthType.OktaSaml when !string.IsNullOrEmpty(singleLogoutServiceUri) && !string.IsNullOrEmpty(logoutRedirectSaml):
            case AuthType.Saml2 when !string.IsNullOrEmpty(singleLogoutServiceUri) && !string.IsNullOrEmpty(logoutRedirectSaml):
                Response.Redirect($"{singleLogoutServiceUri}{HttpUtility.UrlEncode(logoutRedirectSaml)}");
                return;

            case AuthType.OktaSaml:
            case AuthType.Saml2:
                Response.Redirect("~/Logout.ashx");
                return;

            case AuthType.OpenId when !string.IsNullOrEmpty(postLogoutRedirectUri):
                Response.Redirect($"{logoutRedirectOpen}");
                return;

            case AuthType.Okta when !string.IsNullOrEmpty(postLogoutRedirectUriOkta):
                Response.Redirect($"{postLogoutRedirectUriOkta}");
                return;

            case AuthType.OpenId when string.IsNullOrEmpty(postLogoutRedirectUri):
            case AuthType.Azure:
            case AuthType.AzureImplicit:
            case AuthType.AzureFederation:
                var authenticationTypes = HttpContext.Current.Request.GetOwinContext()
                    .Authentication.GetAuthenticationTypes()
                    .Select(o => o.AuthenticationType).ToArray();

                Request.GetOwinContext()
                    .Authentication
                    .SignOut(authenticationTypes);
                break;

            default:
                return;
        }

        Clear();
    }

    private void Clear()
    {
        Response.Cookies.Clear();

        HttpContext.Current.Response.Cache.SetExpires(DateTime.UtcNow.AddDays(-1));
        HttpContext.Current.Response.Cache.SetValidUntilExpires(false);
        HttpContext.Current.Response.Cache.SetRevalidation(HttpCacheRevalidation.AllCaches);
        HttpContext.Current.Response.Cache.SetCacheability(HttpCacheability.NoCache);
        HttpContext.Current.Response.Cache.SetNoStore();
        HttpContext.Current.Application.RemoveAll();
        HttpContext.Current.Items.Clear();
        HttpContext.Current.Response.Cookies.Clear();

        Session.Clear();

        Request.GetOwinContext()?.Environment.Clear();
    }

    private static string VerifyAndLoginUser(string userName, bool mobile)
    {
        using var ws = new WSold();
        ws.Url = ConfigurationManager.AppSettings["wsActiveinfo.webservice"];

        var oggi = DateTime.Now;

        Log.DebugFormat(
            "MD5toHash64(userName: {0} + Oggi:{1} + AccessKey:{2})",
            userName, oggi, ConfigurationManager.AppSettings["AccessKey"]);

        var strMac = (userName + oggi + ConfigurationManager.AppSettings["AccessKey"]).Md5ToHash64();

        Log.DebugFormat("MAC: {0}, parametri: Oggi:{1}, AccessKey: {2}",
            strMac, oggi, ConfigurationManager.AppSettings["AccessKey"]);

        try
        {
            Log.DebugFormat("Call WS with Base-URL {0}", ws.Url);

            Log.DebugFormat("Call WS: ValidateUser2(userName: {0}, Data: {1}, strMac: {2})",
                userName, oggi, strMac);

            var mobileUrl = ConfigurationManager.AppSettings["activeinfo.mobileUrl"];

            if (ws.ValidateUser2(userName, oggi, strMac))
            {
                Log.DebugFormat("User VALID");

                var url = ConfigurationManager.AppSettings["wsActiveinfo.webservice"];

                var uri = new UriBuilder(url);

                var index = uri.Uri.Segments.Length - 2;

                var segment = uri.Uri.Segments[index > 0 ? index : 1];

                var lastOrDefault = uri.Uri.Segments.LastOrDefault();

                var last = segment.Equals(lastOrDefault) ? "" : lastOrDefault;

                url = $"{uri.Scheme}://{uri.Host}:{uri.Port}/{segment}/{last}".TrimEnd('/');

                var utente = userName;

                if (userName.Split('\\').Length > 1)
                    utente = userName.Split('\\')[1];

                Log.DebugFormat("Call MD5toHash64(utente: {0} + Data: {1} + url: {2} + accessKey: {3})",
                    utente, oggi, url, ConfigurationManager.AppSettings["AccessKey"]);

                strMac = (utente + oggi + url + ConfigurationManager.AppSettings["AccessKey"]).Md5ToHash64();

                Log.DebugFormat("Response MD5toHash64: {0}", strMac);

                string urlFromWs;

                if (!mobile)
                {
                    Log.DebugFormat("Call ws.GetAccessTicket2(utente: {0}, Oggi: {1}, url: {2}, strMac: {3})",
                        utente, oggi, url, strMac);

                    urlFromWs = ws.GetAccessTicket2(utente, oggi, url, strMac);

                    Log.DebugFormat("Response GetAccessTicket2: {0}", urlFromWs);
                }
                else
                {
                    Log.DebugFormat("Call ws.GetAccessTicketMobile(utente: {0}, Oggi: {1}, url: {2}, strMac: {3})",
                        utente, oggi, url, strMac);

                    urlFromWs = ws.GetAccessTicketMobile(utente, oggi, url, strMac);

                    Log.DebugFormat("Response GetAccessTicketMobile: {0}", urlFromWs);
                }

                CheckUrl(mobile, mobileUrl, ref urlFromWs, in url);

                Log.DebugFormat("Redirect by CheckUrl: {0}", urlFromWs);

                return urlFromWs;
            }

            Log.DebugFormat("User INVALID");

            return RedirectActiveinfoLoginUrl("InvalidUsername", mobile);
        }
        catch (WebException ex)
        {
            Log.DebugFormat("Verify Error {0} - {1}", ex.GetBaseException().Message, ParseExceptionRespose(ex));

            return RedirectActiveinfoLoginUrl("WebException", mobile);
        }
        catch (Exception ex)
        {
            Log.Error("Errore in fase di chiamata al WS", ex);

            return RedirectActiveinfoLoginUrl("UnhandledException", mobile);
        }
    }

    private string VerifyIdentity(SamlResponse? response = null, bool mobile = false)
    {
        var owinContext = HttpContext.Current?.Request.GetOwinContext();

        var identity = HttpContext.Current?.User.Identity ??
                       owinContext?.Authentication.User.Identity ?? owinContext?.Request.User.Identity;

        var userClaims = User.Identity as System.Security.Claims.ClaimsIdentity ??
                         owinContext?.Authentication.User.Identity as System.Security.Claims.ClaimsIdentity ??
                         owinContext?.Request.User.Identity as System.Security.Claims.ClaimsIdentity;

        _authenticatedUserName = Startup.Auth is AuthType.Saml2 || Startup.Auth is AuthType.OktaSaml
            ? FindUserInResponse(response) ?? FindInEncryptedAssertion() ?? LookupUserByIdentifier(Saml20Identity.Current)
            : userClaims?.FindFirst("preferred_username")?.Value ?? userClaims?.FindFirst("email")?.Value ?? userClaims?.FindFirst("username")?.Value ?? identity?.Name;

        var authType = Startup.Auth is AuthType.Saml2
            ? Saml20Identity.Current?.AuthenticationType ?? $"{Startup.Auth:G}"
            : identity?.AuthenticationType;

        Log.DebugFormat("AuthenticationType: {0}, UserName: {1}", authType, _authenticatedUserName);

        if (string.IsNullOrEmpty(_authenticatedUserName))
        {
            Log.DebugFormat("Redirect to login page because username is empty - from {0} with Authentication {1}",
                Request.Url, authType);

            return RedirectActiveinfoLoginUrl("UsernameEmpty", mobile);
        }

        Log.DebugFormat("Verify user - {0} - from {1} with Authentication {2}",
            _authenticatedUserName, Request.Url, authType);

        switch (_samlResponse?.Assertion)
        {
            case null when string.IsNullOrEmpty(_authenticatedUserName):
                ExtendAuthCookieAction.SignOnAction(_saml2Assertion!, _authenticatedUserName);
                break;

            case not null:
                ExtendAuthCookieAction.SignOnAction(_samlResponse.Assertion);
                break;
        }

        return VerifyAndLoginUser(_authenticatedUserName!, mobile);
    }

    private string? FindInEncryptedAssertion()
    {
        var namedClaims = new List<string> { "preferred_username", "email", "username" };

        _saml2Assertion = new Saml20SignonHandler().HandleResponseNoSign(HttpContext.Current);

        return _saml2Assertion?.Attributes
            .FirstOrDefault(attr =>
            {
                return namedClaims.Exists(x => attr.FriendlyName.ToLower().EndsWith(x));
            })?.AttributeValue[0] ?? null;
    }

    private void CheckSamlIdentity()
    {
        if (Saml20Identity.Current == null) return;

        Log.DebugFormat("Saml20Identity IsAuthenticate: {0}", Saml20Identity.Current.IsAuthenticated);

        // Ensure that the necessary attributes have been returned
        if (!Saml20Identity.Current.HasAttribute("identifier"))
        {
            Log.DebugFormat("SAML Response did not contain the identifier attribute.");
            return;
        }

        // Ensure that there is a value passed back for identifier
        if (!Saml20Identity.Current["identifier"].Any(x => x.AttributeValue.Length > 0))
        {
            Log.DebugFormat("SAML Response contained the identifier attribute, but did not include a value.");
            return;
        }

        Log.DebugFormat("Claims: ");

        foreach (var currentClaim in Saml20Identity.Current.Claims)
        {
            Log.DebugFormat("Type {0} - Value {1} - ValueType {2} - Subject {3}",
                currentClaim.Type, currentClaim.Value, currentClaim.ValueType, currentClaim.Subject);
        }

        Log.DebugFormat("Check Identifier and user");

        var identifier = Saml20Identity.Current["identifier"]?.FirstOrDefault(x => x.AttributeValue.Length > 0)?.AttributeValue.FirstOrDefault();

        _authenticatedUserName = LookupUserByIdentifier(Saml20Identity.Current) ?? FindInEncryptedAssertion();

        Log.DebugFormat("SAML2 Identifier: {0} - user: {1}", identifier, _authenticatedUserName);
    }

    private bool ManageSaml2Response()
    {
        if (Request.QueryString["Action"] == "logout" && (Saml20Identity.Current?.IsAuthenticated ?? true))
        {
            ExtendAuthCookieAction.LogoutAction();
            Logout();
            return true;
        }

        Log.DebugFormat("Get Saml2 Response if any");

        _samlResponse = GetSamlResponse();

        CheckSamlIdentity();

        if (_samlResponse != null || (Saml20Identity.IsInitialized() && Saml20Identity.Current.IsAuthenticated))
        {
            return false;
        }

        OpenIdChallenge();

        return true;
    }

    private bool ManageOAuthRequest()
    {
        Log.DebugFormat("Oauth is authenticated: {0}", User.Identity.IsAuthenticated);

        if (Request.QueryString["Action"] == "logout" && User.Identity.IsAuthenticated)
        {
            Log.DebugFormat("Logout from request action: {0}", Request.QueryString["Action"]);

            Logout();

            return true;
        }

        var owinContext = HttpContext.Current?.Request.GetOwinContext();

        var identityIsAuthenticated = (HttpContext.Current?.User.Identity.IsAuthenticated ?? false) || Request.IsAuthenticated ||
                                      User.Identity.IsAuthenticated || (owinContext?.Authentication.User.Identity.IsAuthenticated ?? false) ||
                                      (owinContext?.Request.User.Identity.IsAuthenticated ?? false);
        if (identityIsAuthenticated)
        {
            Log.DebugFormat("Logged from IdP: {0}", Request.Url);

            return false;
        }

        Log.DebugFormat("Try to challenge a login from: {0}", Request.Url);

        OpenIdChallenge();

        return true;
    }

    private SamlResponse? GetSamlResponse()
    {
        var rawSamlData = Request["SAMLResponse"] ?? "";

        Log.DebugFormat("SAMLResponse: {0}", rawSamlData);

        if (rawSamlData.Contains('%'))
        {
            rawSamlData = HttpUtility.UrlDecode(rawSamlData);
        }

        var samlData = string.IsNullOrEmpty(rawSamlData) ? [] : Convert.FromBase64String(rawSamlData);

        var samlAssertion = Encoding.UTF8.GetString(samlData).Replace("xsi:type=\"xs:string\"", "").Replace("xsi:type=\"xs:anyType\"", "").Replace("xsi:type=\"xsd:anyType\"", "");

        if (string.IsNullOrEmpty(samlAssertion)) return null;
        var serializer = new XmlSerializer(typeof(SamlResponse));

        var samlResponse = (from t in XDocument.Parse(samlAssertion).Elements()
                            select (SamlResponse)serializer.Deserialize(t.CreateReader())).FirstOrDefault();

        return samlResponse;
    }

    private static void CheckUrl(bool mobile, string mobileUrl, ref string urlWs, in string url)
    {
        if (!mobile)
        {
            CheckUrl(ref urlWs, in url);
        }
        else
        {
            CheckUrl(ref urlWs, in mobileUrl);
        }
    }

    private static void CheckUrl(ref string urlWs, in string url)
    {
        var uriFromWs = new UriBuilder(urlWs);
        var uriFromSetting = new Uri(url);

        Log.DebugFormat("Url from WS: {0}", urlWs);

        Log.DebugFormat("Url from Settings: {0}", url);

        uriFromWs.Scheme = uriFromSetting.Scheme;
        uriFromWs.Port = uriFromSetting.Port;
        uriFromWs.Host = uriFromSetting.Host;

        urlWs = uriFromWs.ToString();
    }

    private static string? FindUserInResponse(SamlResponse? response)
    {
        if (response is null) return null;
        
        var success = response.Status?.StatusCode?.Value?.ToLowerInvariant().Contains("success") ?? false;
        
        if (!success) return null;

        return response.EncryptedAssertion != null
            ? null
            : response.Assertion?.AttributeStatement?.Attribute?.AttributeValue?.Text ?? response.Assertion?.Subject?.NameID?.Text;
    }

    private static string? LookupUserByIdentifier(IIdentity? identifier)
    {
        return identifier?.Name ?? null;
    }

    private static string RedirectActiveinfoLoginUrl(string utente = "", bool mobile = false)
    {
        if (!string.IsNullOrEmpty(utente))
            utente = $"&user={utente}";

        if (mobile)
            return $"{ConfigurationManager.AppSettings["activeinfo.mobileUrl"]}?{utente}";

        var url = ConfigurationManager.AppSettings["wsActiveinfo.webservice"];

        url = url.Contains("webservice.asmx")
            ? url.ToLower().Replace("webservice.asmx", $"Login2.aspx?SSO=False{utente}")
            : url.ToLower().Replace("webservicecs.asmx", $"Login2.aspx?SSO=False{utente}");

        return url;
    }

    private static string ParseExceptionRespose(WebException exception)
    {
        try
        {
            var descrption = ((HttpWebResponse)exception.Response)?.GetResponseStream();
            if (descrption == null) return exception.Message;
            using var readStream = new StreamReader(descrption);
            var responseContents = readStream.ReadToEnd();

            return responseContents;
        }
        catch (Exception)
        {
            return exception.Message;
        }
    }
}
