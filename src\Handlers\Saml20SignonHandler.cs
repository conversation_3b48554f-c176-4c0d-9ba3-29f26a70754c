using log4net;

using SAML2;
using SAML2.Bindings;
using SAML2.Config;
using SAML2.Exceptions;
using SAML2.Protocol;
using SAML2.Protocol.Pages;
using SAML2.Schema.Core;
using SAML2.Schema.Metadata;
using SAML2.Schema.Protocol;
using SAML2.Specification;
using SAML2.Utils;

using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Web;
using System.Web.Caching;
using System.Xml;
#pragma warning disable S2325
#pragma warning disable S3973
#pragma warning disable S3011
#pragma warning disable S4426
#pragma warning disable S1144

namespace ActiveInfoSSO.Handlers;

public sealed class Saml20SignonHandler : Saml20AbstractEndpointHandler
{
    private new static readonly ILog Logger = LogManager.GetLogger(typeof(Saml20SignonHandler));

    private const string ExpectedInResponseToSessionKey = "ExpectedInResponseTo";

    private readonly X509Certificate2 _certificate;

    public Saml20SignonHandler()
    {
        _certificate = Saml2Config.Current.ServiceProvider.SigningCertificate.GetCertificate();

        try
        {
            RedirectUrl = Saml2Config.Current.ServiceProvider.SignOnEndpoint.RedirectUrl;
        }
        catch (Exception e)
        {
            Logger.Error(e.Message, e);
        }
    }

    #region Public methods

    public static IEnumerable<AsymmetricAlgorithm> GetTrustedSigners(ICollection<KeyDescriptor> keys,
        IdentityProvider identityProvider)
    {
        if (keys == null) throw new ArgumentNullException(nameof(keys));

        var result = new List<AsymmetricAlgorithm>(keys.Count);
        foreach (var keyDescriptor in keys)
        foreach (KeyInfoClause clause in (KeyInfo)keyDescriptor.KeyInfo)
        {
            if (clause is KeyInfoX509Data x509Data)
            {
                var cert = XmlSignatureUtils.GetCertificateFromKeyInfo(x509Data);
                if (!CertificateSatisfiesSpecifications(identityProvider, cert)) continue;
            }

            var key = XmlSignatureUtils.ExtractKey(clause);
            result.Add(key);
        }

        return result;
    }

    internal static XmlElement GetAssertion(XmlElement el, out bool isEncrypted)
    {
        Logger.Debug(TraceMessages.AssertionParse);

        var encryptedList = el.GetElementsByTagName(EncryptedAssertion.ElementName, Saml20Constants.Assertion);
        if (encryptedList.Count == 1)
        {
            isEncrypted = true;
            var encryptedAssertion = (XmlElement)encryptedList[0];

            Logger.DebugFormat(TraceMessages.EncryptedAssertionFound, encryptedAssertion.OuterXml);

            return encryptedAssertion;
        }

        var assertionList = el.GetElementsByTagName(Assertion.ElementName, Saml20Constants.Assertion);
        if (assertionList.Count == 1)
        {
            isEncrypted = false;
            var assertion = (XmlElement)assertionList[0];

            Logger.DebugFormat(TraceMessages.AssertionFound, assertion.OuterXml);

            return assertion;
        }

        Logger.Warn(ErrorMessages.AssertionNotFound);

        isEncrypted = false;
        return null;
    }

    #endregion Public methods

    #region Protected methods

    protected override void Handle(HttpContext context)
    {
        Logger.Debug(TraceMessages.SignOnHandlerCalled);

        if (Array.Exists(context.Request.Headers.AllKeys, s => s == SoapConstants.SoapAction))
        {
            HandleSoap(context, context.Request.InputStream);
            return;
        }

        if (!string.IsNullOrEmpty(context.Request.Params["SAMLart"])) HandleArtifact(context);

        if (!string.IsNullOrEmpty(context.Request.Params["SamlResponse"]))
        {
            HandleResponse(context);
        }
        else
        {
            if (Saml2Config.Current.CommonDomainCookie.Enabled && context.Request.QueryString["r"] == null &&
                context.Request.Params["cidp"] == null)
            {
                Logger.Debug(TraceMessages.CommonDomainCookieRedirectForDiscovery);
                context.Response.Redirect(Saml2Config.Current.CommonDomainCookie.LocalReaderEndpoint);
            }
            else
            {
                Logger.WarnFormat(ErrorMessages.UnauthenticatedAccess, context.Request.RawUrl);
                SendRequest(context);
            }
        }
    }

    private static void PreHandleAssertion(XmlElement elem, IdentityProvider endpoint)
    {
        Logger.DebugFormat(TraceMessages.AssertionPrehandlerCalled);

        if (endpoint == null || endpoint.LogoutEndpoint == null ||
            string.IsNullOrEmpty(endpoint.LogoutEndpoint.TokenAccessor)) return;

        if (Activator.CreateInstance(Type.GetType(endpoint.LogoutEndpoint.TokenAccessor, false) ?? typeof(string)) is
            ISaml20IdpTokenAccessor idpTokenAccessor)
        {
            Logger.DebugFormat("{0}.{1} called", idpTokenAccessor.GetType(), "ReadToken");
            idpTokenAccessor.ReadToken(elem);
            Logger.DebugFormat("{0}.{1} finished", idpTokenAccessor.GetType(), "ReadToken");
        }
    }

    #endregion Protected methods

    #region Private methods - Helpers

    private static bool CertificateSatisfiesSpecifications(IdentityProvider idp, X509Certificate2 cert)
    {
        return SpecificationFactory.GetCertificateSpecifications(idp).All(spec => spec.IsSatisfiedBy(cert));
    }

    private static void CheckReplayAttack(XmlElement element)
    {
        Logger.Debug(TraceMessages.ReplayAttackCheck);

        var inResponseToAttribute = element.Attributes["InResponseTo"];
        if (inResponseToAttribute == null)
            throw new Saml20Exception(ErrorMessages.ResponseMissingInResponseToAttribute);

        var inResponseTo = inResponseToAttribute.Value;
        if (string.IsNullOrEmpty(inResponseTo)) throw new Saml20Exception(ErrorMessages.ExpectedInResponseToEmpty);

        var expectedInResponseTo = StateService.Get<string>(ExpectedInResponseToSessionKey);
        if (string.IsNullOrEmpty(expectedInResponseTo))
            throw new Saml20Exception(ErrorMessages.ExpectedInResponseToMissing);

        if (inResponseTo != expectedInResponseTo)
        {
            Logger.ErrorFormat(ErrorMessages.ReplayAttack, inResponseTo, expectedInResponseTo);
            throw new Saml20Exception(string.Format(ErrorMessages.ReplayAttack, inResponseTo,
                expectedInResponseTo));
        }

        Logger.Debug(TraceMessages.ReplaceAttackCheckCleared);

        StateService.Remove(ExpectedInResponseToSessionKey);
    }

    private static XmlDocument GetDecodedSamlResponse(HttpContext context, Encoding encoding)
    {
        Logger.Debug(TraceMessages.SamlResponseDecoding);

        var base64 = context.Request.Params["SAMLResponse"];

        var doc = new XmlDocument { PreserveWhitespace = true };
        var samlResponse = encoding.GetString(Convert.FromBase64String(base64));
        doc.LoadXml(samlResponse);

        Logger.DebugFormat(TraceMessages.SamlResponseDecoded, samlResponse);

        return doc;
    }

    private static Saml20EncryptedAssertion GetDecryptedAssertion(XmlElement elem)
    {
        Logger.Debug(TraceMessages.EncryptedAssertionDecrypting);

        var decryptedAssertion = new Saml20EncryptedAssertion((RSA)Saml2Config.Current.ServiceProvider
            .SigningCertificate.GetCertificate().PrivateKey);
        decryptedAssertion.LoadXml(elem);
        decryptedAssertion.Decrypt();

        Logger.DebugFormat(TraceMessages.EncryptedAssertionDecrypted,
            decryptedAssertion.Assertion.DocumentElement?.OuterXml);

        return decryptedAssertion;
    }

    private static string GetIssuer(XmlElement assertion)
    {
        var result = string.Empty;
        var list = assertion.GetElementsByTagName("Issuer", Saml20Constants.Assertion);
        if (list.Count > 0)
        {
            var issuer = (XmlElement)list[0];
            result = issuer.InnerText;
        }

        return result;
    }

    private static Status GetStatusElement(XmlElement element)
    {
        var statElem = element.GetElementsByTagName(Status.ElementName, Saml20Constants.Protocol)[0];
        return Serialization.DeserializeFromXmlString<Status>(statElem.OuterXml);
    }

    #endregion Private methods - Helpers

    #region Private methods - Handlers

    private void DoSignOn(HttpContext context, Saml20Assertion assertion)
    {
        StateService.Set(IdpLoginSessionKey, StateService.Get<string>(IdpTempSessionKey));
        StateService.Set(IdpSessionIdKey, assertion.SessionIndex);
        StateService.Set(IdpNameIdFormat, assertion.Subject.Format);
        StateService.Set(IdpNameId, assertion.Subject.Value);

        Logger.DebugFormat(TraceMessages.SignOnProcessed, assertion.SessionIndex, assertion.Subject.Value,
            assertion.Subject.Format);

        Logger.Debug(TraceMessages.SignOnActionsExecuting);
        foreach (var action in SAML2.Actions.Actions.GetActions())
        {
            Logger.DebugFormat("{0}.{1} called", action.GetType(), "LoginAction()");

            action.SignOnAction(this, context, assertion);

            Logger.DebugFormat("{0}.{1} finished", action.GetType(), "LoginAction()");
        }
    }

    private void HandleArtifact(HttpContext context)
    {
        var builder = new HttpArtifactBindingBuilder(context);
        var inputStream = builder.ResolveArtifact();

        HandleSoap(context, inputStream);
    }

    private void HandleAssertion(HttpContext context, XmlElement elem)
    {
        Logger.DebugFormat(TraceMessages.AssertionProcessing, elem.OuterXml);

        var issuer = GetIssuer(elem);
        var endp = RetrieveIDPConfiguration(issuer);

        PreHandleAssertion(elem, endp);

        if (endp == null || endp.Metadata == null)
        {
            Logger.Error(ErrorMessages.AssertionIdentityProviderUnknown);
            throw new Saml20Exception(ErrorMessages.AssertionIdentityProviderUnknown);
        }

        var quirksMode = endp.QuirksMode;
        var assertion = new Saml20Assertion(elem, null, Saml2Config.Current.AssertionProfile.AssertionValidator,
            quirksMode);

        if (!endp.OmitAssertionSignatureCheck &&
            !assertion.CheckSignature(GetTrustedSigners(endp.Metadata.GetKeys(KeyTypes.Signing), endp)))
        {
            Logger.Error(ErrorMessages.AssertionSignatureInvalid);
            throw new Saml20Exception(ErrorMessages.AssertionSignatureInvalid);
        }

        if (assertion.IsExpired)
        {
            Logger.Error(ErrorMessages.AssertionExpired);
            throw new Saml20Exception(ErrorMessages.AssertionExpired);
        }

        if (assertion.IsOneTimeUse)
        {
            if (context.Cache[assertion.Id] != null)
            {
                Logger.Error(ErrorMessages.AssertionOneTimeUseExceeded);
                throw new Saml20Exception(ErrorMessages.AssertionOneTimeUseExceeded);
            }

            context.Cache.Insert(assertion.Id, string.Empty, null, assertion.NotOnOrAfter,
                Cache.NoSlidingExpiration);
        }

        Logger.DebugFormat(TraceMessages.AssertionParsed, assertion.Id);

        DoSignOn(context, assertion);
    }

    private Saml20Assertion HandleAssertionNoSign(XmlElement elem)
    {
        Logger.DebugFormat(TraceMessages.AssertionProcessing, elem.OuterXml);

        var issuer = GetIssuer(elem);
        var endp = RetrieveIDPConfiguration(issuer);

        PreHandleAssertion(elem, endp);

        if (endp?.Metadata == null)
        {
            Logger.Error(ErrorMessages.AssertionIdentityProviderUnknown);
            throw new Saml20Exception(ErrorMessages.AssertionIdentityProviderUnknown);
        }

        var assertion = new Saml20Assertion(elem, null, Saml2Config.Current.AssertionProfile.AssertionValidator,
            endp.QuirksMode, false);

        Logger.DebugFormat(TraceMessages.AssertionParsed, assertion.Id);

        return assertion;
    }

    private void HandleEncryptedAssertion(HttpContext context, XmlElement elem)
    {
        HandleAssertion(context, GetDecryptedAssertion(elem).Assertion.DocumentElement);
    }

    public void HandleResponse(HttpContext context)
    {
        var defaultEncoding = Encoding.UTF8;
        var doc = GetDecodedSamlResponse(context, defaultEncoding);
        Logger.DebugFormat(TraceMessages.SamlResponseReceived, doc.OuterXml);

        var status = GetStatusElement(doc.DocumentElement);
        if (status.StatusCode.Value != Saml20Constants.StatusCodes.Success)
        {
            if (status.StatusCode.Value == Saml20Constants.StatusCodes.NoPassive)
            {
                Logger.Error(ErrorMessages.ResponseStatusIsNoPassive);
                throw new Saml20Exception(ErrorMessages.ResponseStatusIsNoPassive);
            }

            Logger.ErrorFormat(ErrorMessages.ResponseStatusNotSuccessful, status.StatusCode.Value);
            throw new Saml20Exception(string.Format(ErrorMessages.ResponseStatusNotSuccessful,
                status.StatusCode.Value));
        }

        var assertion = GetAssertion(doc.DocumentElement, out var isEncrypted);
        if (isEncrypted) assertion = GetDecryptedAssertion(assertion).Assertion.DocumentElement;

        var issuer = GetIssuer(assertion);
        var endpoint = RetrieveIDPConfiguration(issuer);

        if (!endpoint.AllowUnsolicitedResponses) CheckReplayAttack(doc.DocumentElement);

        if (!string.IsNullOrEmpty(endpoint.ResponseEncoding))
        {
            Encoding encodingOverride;
            try
            {
                encodingOverride = Encoding.GetEncoding(endpoint.ResponseEncoding);
            }
            catch (ArgumentException ex)
            {
                Logger.ErrorFormat(ErrorMessages.UnknownEncoding, endpoint.ResponseEncoding);
                throw new ArgumentException(string.Format(ErrorMessages.UnknownEncoding, endpoint.ResponseEncoding),
                    ex);
            }

            if (encodingOverride.CodePage != defaultEncoding.CodePage)
            {
                var doc1 = GetDecodedSamlResponse(context, encodingOverride);
                assertion = GetAssertion(doc1.DocumentElement, out isEncrypted);
            }
        }

        HandleAssertion(context, assertion);
    }

    public Saml20Assertion HandleResponseNoSign(HttpContext context)
    {
        var defaultEncoding = Encoding.UTF8;
        var doc = GetDecodedSamlResponse(context, defaultEncoding);
        Logger.DebugFormat(TraceMessages.SamlResponseReceived, doc.OuterXml);

        var status = GetStatusElement(doc.DocumentElement);
        if (status.StatusCode.Value != Saml20Constants.StatusCodes.Success)
        {
            if (status.StatusCode.Value == Saml20Constants.StatusCodes.NoPassive)
            {
                Logger.Error(ErrorMessages.ResponseStatusIsNoPassive);
                throw new Saml20Exception(ErrorMessages.ResponseStatusIsNoPassive);
            }

            Logger.ErrorFormat(ErrorMessages.ResponseStatusNotSuccessful, status.StatusCode.Value);
            throw new Saml20Exception(string.Format(ErrorMessages.ResponseStatusNotSuccessful,
                status.StatusCode.Value));
        }

        var assertion = GetAssertion(doc.DocumentElement, out var isEncrypted);
        if (isEncrypted) assertion = GetDecryptedAssertion(assertion).Assertion.DocumentElement;

        var issuer = GetIssuer(assertion);
        var endpoint = RetrieveIDPConfiguration(issuer);

        if (!endpoint.AllowUnsolicitedResponses) CheckReplayAttack(doc.DocumentElement);

        if (!string.IsNullOrEmpty(endpoint.ResponseEncoding))
        {
            Encoding encodingOverride;
            try
            {
                encodingOverride = Encoding.GetEncoding(endpoint.ResponseEncoding);
            }
            catch (ArgumentException ex)
            {
                Logger.ErrorFormat(ErrorMessages.UnknownEncoding, endpoint.ResponseEncoding);
                throw new ArgumentException(string.Format(ErrorMessages.UnknownEncoding, endpoint.ResponseEncoding),
                    ex);
            }

            if (encodingOverride.CodePage != defaultEncoding.CodePage)
            {
                var doc1 = GetDecodedSamlResponse(context, encodingOverride);
                assertion = GetAssertion(doc1.DocumentElement, out isEncrypted);
            }
        }

        return HandleAssertionNoSign(assertion);
    }

    private void HandleSoap(HttpContext context, Stream inputStream)
    {
        var parser = new HttpArtifactBindingParser(inputStream);
        Logger.DebugFormat(TraceMessages.SOAPMessageParse, parser.SamlMessage.OuterXml);

        var builder = new HttpArtifactBindingBuilder(context);

        if (parser.IsArtifactResolve)
        {
            Logger.Debug(TraceMessages.ArtifactResolveReceived);

            var idp = RetrieveIDPConfiguration(parser.Issuer);
            if (!parser.CheckSamlMessageSignature(idp.Metadata.Keys))
            {
                Logger.Error(ErrorMessages.ArtifactResolveSignatureInvalid);
                throw new Saml20Exception(ErrorMessages.ArtifactResolveSignatureInvalid);
            }

            builder.RespondToArtifactResolve(parser.ArtifactResolve);
        }
        else if (parser.IsArtifactResponse)
        {
            Logger.Debug(TraceMessages.ArtifactResolveReceived);

            var idp = RetrieveIDPConfiguration(parser.Issuer);
            if (!parser.CheckSamlMessageSignature(idp.Metadata.Keys))
            {
                Logger.Error(ErrorMessages.ArtifactResponseSignatureInvalid);
                throw new Saml20Exception(ErrorMessages.ArtifactResponseSignatureInvalid);
            }

            var status = parser.ArtifactResponse.Status;
            if (status.StatusCode.Value != Saml20Constants.StatusCodes.Success)
            {
                Logger.ErrorFormat(ErrorMessages.ArtifactResponseStatusCodeInvalid, status.StatusCode.Value);
                throw new Saml20Exception(string.Format(ErrorMessages.ArtifactResponseStatusCodeInvalid,
                    status.StatusCode.Value));
            }

            if (parser.ArtifactResponse.Any.LocalName == Response.ElementName)
            {
                if (!idp.AllowUnsolicitedResponses) CheckReplayAttack(parser.ArtifactResponse.Any);

                var responseStatus = GetStatusElement(parser.ArtifactResponse.Any);
                if (responseStatus.StatusCode.Value != Saml20Constants.StatusCodes.Success)
                {
                    Logger.ErrorFormat(ErrorMessages.ArtifactResponseStatusCodeInvalid,
                        responseStatus.StatusCode.Value);
                    throw new Saml20Exception(string.Format(ErrorMessages.ArtifactResponseStatusCodeInvalid,
                        responseStatus.StatusCode.Value));
                }

                var assertion = GetAssertion(parser.ArtifactResponse.Any, out var isEncrypted);
                if (assertion == null)
                {
                    Logger.Error(ErrorMessages.ArtifactResponseMissingAssertion);
                    throw new Saml20Exception(ErrorMessages.ArtifactResponseMissingAssertion);
                }

                if (isEncrypted)
                    HandleEncryptedAssertion(context, assertion);
                else
                    HandleAssertion(context, assertion);
            }
            else
            {
                Logger.ErrorFormat(ErrorMessages.ArtifactResponseMissingResponse);
                throw new Saml20Exception(ErrorMessages.ArtifactResponseMissingResponse);
            }
        }
        else
        {
            Logger.ErrorFormat(ErrorMessages.SoapMessageUnsupportedSamlMessage);
            throw new Saml20Exception(ErrorMessages.SoapMessageUnsupportedSamlMessage);
        }
    }

    private void SendRequest(HttpContext context)
    {
        var returnUrl = context.Request.QueryString["ReturnUrl"];
        if (!string.IsNullOrEmpty(returnUrl)) StateService.Set("RedirectUrl", returnUrl);

        var idp = RetrieveIDP(context);
        if (idp == null)
        {
            Logger.DebugFormat(TraceMessages.IdentityProviderRedirect);

            var page = new SelectSaml20IDP();
            page.ProcessRequest(context);
            return;
        }

        var authnRequest = Saml20AuthnRequest.GetDefault();
        TransferClient(idp, authnRequest, context);
    }

    private static IdentityProviderEndpoint DetermineEndpointConfiguration(
        BindingType defaultBinding,
        IdentityProviderEndpoint config,
        List<IdentityProviderEndpoint> metadata)
    {
        var result = new IdentityProviderEndpoint
        {
            Binding = defaultBinding
        };
        if (config != null)
            result.Binding = config.Binding;
        else if (!metadata.Exists(el => el.Binding == defaultBinding))
            result.Binding = result.Binding == BindingType.Post ? BindingType.Redirect : BindingType.Post;
        if (config != null && !string.IsNullOrEmpty(config.Url))
            result.Url = config.Url;
        else
            result.Url = (metadata.Find(el => el.Binding == result.Binding) ??
                          throw new ConfigurationErrorsException(
                              $"No IdentityProvider supporting SAML binding {result.Binding} found in metadata"))
                .Url;
        return result;
    }

    private void TransferClient(IdentityProvider identityProvider, Saml20AuthnRequest request, HttpContext context)
    {
        StateService.Set(IdpTempSessionKey, identityProvider.Id);

        var destination = DetermineEndpointConfiguration(BindingType.Redirect, identityProvider.SignOnEndpoint,
            identityProvider.Metadata.SSOEndpoints);
        request.Destination = destination.Url;

        if (identityProvider.ForceAuth) request.ForceAuthn = true;

        var isPassiveFlag = StateService.Get<bool?>(IdpIsPassive);

        if (isPassiveFlag != null && (bool)isPassiveFlag)
        {
            request.IsPassive = true;
            StateService.Set(IdpIsPassive, null);
        }

        if (identityProvider.IsPassive) request.IsPassive = true;

        var forceAuthnFlag = StateService.Get<bool?>(IdpForceAuthn);
        if (forceAuthnFlag != null && (bool)forceAuthnFlag)
        {
            request.ForceAuthn = true;
            StateService.Set(IdpForceAuthn, null);
        }

        if (identityProvider.SignOnEndpoint != null &&
            !string.IsNullOrEmpty(identityProvider.SignOnEndpoint.ForceProtocolBinding))
            request.ProtocolBinding = identityProvider.SignOnEndpoint.ForceProtocolBinding;

        StateService.Set(ExpectedInResponseToSessionKey, request.Id);

        switch (destination.Binding)
        {
            case BindingType.Redirect:
                Logger.DebugFormat(TraceMessages.AuthnRequestPrepared, identityProvider.Id,
                    Saml20Constants.ProtocolBindings.HttpRedirect);

                var redirectBuilder = new HttpRedirectBindingBuilder
                {
                    SigningKey = _certificate.PrivateKey,
                    Request = request.GetXml().OuterXml
                };

                Logger.DebugFormat(TraceMessages.AuthnRequestSent, redirectBuilder.Request);

                var redirectLocation = request.Destination + (request.Destination.Contains("?") ? "&" : "?") +
                                       redirectBuilder.ToQuery();
                context.Response.Redirect(redirectLocation, true);
                break;

            case BindingType.Post:
                Logger.DebugFormat(TraceMessages.AuthnRequestPrepared, identityProvider.Id,
                    Saml20Constants.ProtocolBindings.HttpPost);

                var postBuilder = new HttpPostBindingBuilder(destination);

                if (string.IsNullOrEmpty(request.ProtocolBinding))
                    request.ProtocolBinding = Saml20Constants.ProtocolBindings.HttpPost;

                var requestXml = request.GetXml();
                XmlSignatureUtils.SignDocument(requestXml, request.Id);
                postBuilder.Request = requestXml.OuterXml;

                Logger.DebugFormat(TraceMessages.AuthnRequestSent, postBuilder.Request);

                postBuilder.GetPage().ProcessRequest(context);
                break;

            case BindingType.Artifact:
                Logger.DebugFormat(TraceMessages.AuthnRequestPrepared, identityProvider.Id,
                    Saml20Constants.ProtocolBindings.HttpArtifact);

                var artifactBuilder = new HttpArtifactBindingBuilder(context);

                if (string.IsNullOrEmpty(request.ProtocolBinding))
                    request.ProtocolBinding = Saml20Constants.ProtocolBindings.HttpArtifact;

                Logger.DebugFormat(TraceMessages.AuthnRequestSent, request.GetXml().OuterXml);

                artifactBuilder.RedirectFromLogin(destination, request);
                break;

            default:
                Logger.Error(ErrorMessages.EndpointBindingInvalid);
                throw new Saml20Exception(ErrorMessages.EndpointBindingInvalid);
        }
    }

    public static RSACryptoServiceProvider GetCryptoProviderForSha256(RSACryptoServiceProvider rsaProvider)
    {
        const int provRsaAes = 24;

        if ((rsaProvider.CspKeyContainerInfo.ProviderType != 1 && rsaProvider.CspKeyContainerInfo.ProviderType != 12) ||
            rsaProvider.CspKeyContainerInfo.HardwareDevice)
            return rsaProvider;

        var csp = new CspParameters
        {
            ProviderType = provRsaAes,
            KeyContainerName = rsaProvider.CspKeyContainerInfo.KeyContainerName,
            KeyNumber = (int)rsaProvider.CspKeyContainerInfo.KeyNumber
        };

        if (rsaProvider.CspKeyContainerInfo.MachineKeyStore)
        {
            csp.Flags = CspProviderFlags.UseMachineKeyStore;
        }

        csp.Flags |= CspProviderFlags.UseExistingKey;

        try
        {
            return new RSACryptoServiceProvider(csp) { KeySize = 2048 };
        }
        catch (CryptographicException e)
        {
            Logger.Error(e.GetBaseException().Message, e);
            return rsaProvider;
        }
    }

    private static void Sha256NotSupportedWorkaround(X509Certificate2 cert)
    {
        if (cert.GetRSAPrivateKey() is not RSACryptoServiceProvider orgKey) return;
        try
        {
            var rsaCryptoServiceProvider = GetCryptoProviderForSha256(orgKey);
            // Use reflection to set the private key if necessary
            var field = typeof(X509Certificate2).GetField("m_privateKey",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            field?.SetValue(cert, rsaCryptoServiceProvider);
        }
        catch (CryptographicException e)
        {
            Logger.Error(e.GetBaseException().Message, e);
        }
        finally
        {
            if (!ReferenceEquals(orgKey, cert.GetRSAPrivateKey()))
                orgKey.Dispose();
        }
    }

    #endregion Private  methods - Handlers
}