﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="IdentityModel" version="7.0.0" targetFramework="net48" />
  <package id="log4net" version="3.1.0" targetFramework="net48" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.4" targetFramework="net48" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.4" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.Bcl.Memory" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.7" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols.WsFederation" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens.Saml" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Xml" version="8.13.0" targetFramework="net48" />
  <package id="Microsoft.Owin" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security.Jwt" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security.OpenIdConnect" version="4.2.3" targetFramework="net48" />
  <package id="Microsoft.Owin.Security.WsFederation" version="4.2.3" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Okta.AspNet" version="3.2.6" targetFramework="net48" />
  <package id="Okta.AspNet.Abstractions" version="5.1.2" targetFramework="net48" />
  <package id="Owin" version="1.0" targetFramework="net48" />
  <package id="SAML2" version="********" targetFramework="net48" />
  <package id="SAML2.Logging.Log4Net" version="*******" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.7" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="8.13.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.7" targetFramework="net48" />
  <package id="System.Memory" version="4.6.3" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.7" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.7" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net48" />
</packages>